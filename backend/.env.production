# STAGING ENVIRONMENT FOR RENDER (TEMPORARY FOR TESTING)
ENV_MODE=staging

# SUPABASE
SUPABASE_URL=https://irfvjdismpsxwihifsel.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlyZnZqZGlzbXBzeHdpaGlmc2VsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MjIxNDAsImV4cCI6MjA2NTM5ODE0MH0.S_KVhio9_2VpGZPgIieAf22TYeNu3qyUns6HyERqYpI
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlyZnZqZGlzbXBzeHdpaGlmc2VsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTgyMjE0MCwiZXhwIjoyMDY1Mzk4MTQwfQ.Z1k2HbdP-Z1m5mvqRw7ynWrkAPKUTJ11_8bzdDHxAM4

# REDIS RENDER (PRODUCTION) - Using Render Managed Redis (Internal Connection)
REDIS_URL=redis://red-d197f5ffte5s73c39cjg:6379
REDIS_HOST=red-d197f5ffte5s73c39cjg
REDIS_PORT=6379
REDIS_USERNAME=
REDIS_PASSWORD=
REDIS_SSL=false

# RABBITMQ RENDER
RABBITMQ_URL=amqp://guest:guest@connect-rabbitmq:5672
RABBITMQ_HOST=connect-rabbitmq
RABBITMQ_PORT=5672

# FRONTEND URL
NEXT_PUBLIC_URL=https://connect-mu-seven.vercel.app

# API KEYS (PRODUCTION)
DAYTONA_API_KEY=dtn_e650ae7d7b250337033aaa01e4a78d03c11d4135aa1df5dd57045619f36accfd
DAYTONA_SERVER_URL=https://app.daytona.io/api
DAYTONA_TARGET=us

FIRECRAWL_API_KEY=fc-60fe43022991488e9f9d030a4d075b8b
FIRECRAWL_URL=https://api.firecrawl.dev

TAVILY_API_KEY=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
RAPID_API_KEY=250c63d6e4msh224510ad4df47c0p1758b2jsn4767ad2cac62

# AWS (EMPTY FOR NOW)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION_NAME=

# LLM PROVIDERS
ANTHROPIC_API_KEY=sk-ant-api03-placeholder-anthropic-key
OPENAI_API_KEY=sk-placeholder-openai-key
MODEL_TO_USE=claude-3-5-sonnet-20241022
OPENROUTER_API_KEY=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

# GROQ (EMPTY FOR NOW)
GROQ_API_KEY=

# STRIPE CONFIGURATION
STRIPE_SECRET_KEY=***********************************************************************************************************
STRIPE_WEBHOOK_SECRET=whsec_placeholder
